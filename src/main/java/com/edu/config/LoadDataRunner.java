package com.edu.config;

import com.edu.model.Product;
import com.edu.repository.ProductRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Component that loads initial sample data into the database on application startup.
 */
@Component
public class LoadDataRunner implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(LoadDataRunner.class);
    
    private final ProductRepository productRepository;
    private final Random random = new Random();
    
    @Autowired
    public LoadDataRunner(ProductRepository productRepository) {
        this.productRepository = productRepository;
    }
    
    @Override
    public void run(String... args) throws Exception {
        if (productRepository.count() == 0) {
            logger.info("Loading initial sample data...");
            loadSampleData();
            logger.info("Sample data loaded successfully");
        } else {
            logger.info("Database already contains data, skipping initial load");
        }
    }
    
    private void loadSampleData() {
        List<Product> sampleProducts = Arrays.asList(
            // Electronics
            new Product("Laptop Dell XPS 13", "High-performance ultrabook with Intel i7", 1299.99, "Electronics"),
            new Product("Laptop MacBook Pro", "Apple MacBook Pro with M2 chip", 1999.99, "Electronics"),
            new Product("Laptop Gaming ASUS", "Gaming laptop with RTX 4060", 1599.99, "Electronics"),
            new Product("Laptop HP Pavilion", "Budget-friendly laptop for everyday use", 699.99, "Electronics"),
            new Product("Laptop Lenovo ThinkPad", "Business laptop with excellent keyboard", 1199.99, "Electronics"),
            
            // Smartphones
            new Product("iPhone 15 Pro", "Latest iPhone with titanium design", 999.99, "Electronics"),
            new Product("Samsung Galaxy S24", "Android flagship with AI features", 899.99, "Electronics"),
            new Product("Google Pixel 8", "Pure Android experience with great camera", 699.99, "Electronics"),
            
            // Accessories
            new Product("Wireless Mouse Logitech", "Ergonomic wireless mouse", 49.99, "Electronics"),
            new Product("Mechanical Keyboard", "RGB mechanical gaming keyboard", 129.99, "Electronics"),
            new Product("USB-C Hub", "7-in-1 USB-C hub with HDMI", 39.99, "Electronics"),
            new Product("Bluetooth Headphones", "Noise-cancelling wireless headphones", 199.99, "Electronics"),
            
            // Home & Garden
            new Product("Coffee Maker Keurig", "Single-serve coffee maker", 89.99, "Home & Garden"),
            new Product("Air Fryer Ninja", "6-quart air fryer with multiple functions", 119.99, "Home & Garden"),
            new Product("Vacuum Cleaner Dyson", "Cordless stick vacuum", 299.99, "Home & Garden"),
            new Product("Smart Thermostat", "WiFi-enabled programmable thermostat", 149.99, "Home & Garden"),
            
            // Books
            new Product("Programming Book Java", "Complete guide to Java programming", 59.99, "Books"),
            new Product("Data Structures Book", "Algorithms and data structures explained", 49.99, "Books"),
            new Product("Machine Learning Book", "Introduction to ML and AI", 69.99, "Books"),
            
            // Sports
            new Product("Running Shoes Nike", "Lightweight running shoes", 129.99, "Sports"),
            new Product("Yoga Mat Premium", "Non-slip yoga mat with carrying strap", 39.99, "Sports"),
            new Product("Dumbbells Set", "Adjustable dumbbells 5-50 lbs", 299.99, "Sports"),
            
            // Clothing
            new Product("T-Shirt Cotton", "100% cotton comfortable t-shirt", 19.99, "Clothing"),
            new Product("Jeans Levi's", "Classic straight-fit jeans", 79.99, "Clothing"),
            new Product("Hoodie Pullover", "Warm pullover hoodie", 49.99, "Clothing")
        );
        
        // Add some random click and view counts to simulate popularity
        for (Product product : sampleProducts) {
            product.setClickCount(random.nextInt(100));
            product.setViewCount(random.nextInt(500));
        }
        
        productRepository.saveAll(sampleProducts);
    }
}
