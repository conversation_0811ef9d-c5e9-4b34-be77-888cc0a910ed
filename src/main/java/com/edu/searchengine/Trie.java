package com.edu.searchengine;

import java.util.ArrayList;
import java.util.List;

/**
 * Trie data structure for efficient prefix-based search.
 * Supports insertion, deletion, and prefix-based search operations.
 */
public class Trie {
    private TrieNode root;
    
    public Trie() {
        this.root = new TrieNode();
    }
    
    /**
     * Inserts a word into the trie with associated product ID.
     * 
     * @param word The word to insert (case-insensitive)
     * @param productId The ID of the product associated with this word
     */
    public void insert(String word, Long productId) {
        if (word == null || word.trim().isEmpty()) {
            return;
        }
        
        TrieNode current = root;
        String normalizedWord = word.toLowerCase().trim();
        
        for (char ch : normalizedWord.toCharArray()) {
            if (!current.hasChild(ch)) {
                current.addChild(ch);
            }
            current = current.getChild(ch);
        }
        
        current.setEndOfWord(true);
        current.setProductId(productId);
    }
    
    /**
     * Searches for words that start with the given prefix.
     * 
     * @param prefix The prefix to search for
     * @return List of product IDs that match the prefix
     */
    public List<Long> searchByPrefix(String prefix) {
        List<Long> results = new ArrayList<>();
        
        if (prefix == null || prefix.trim().isEmpty()) {
            return results;
        }
        
        String normalizedPrefix = prefix.toLowerCase().trim();
        TrieNode current = root;
        
        // Navigate to the end of the prefix
        for (char ch : normalizedPrefix.toCharArray()) {
            if (!current.hasChild(ch)) {
                return results; // Prefix not found
            }
            current = current.getChild(ch);
        }
        
        // Collect all words that start with this prefix
        collectAllWords(current, results);
        return results;
    }
    
    /**
     * Recursively collects all product IDs from the given node and its children.
     * 
     * @param node The starting node
     * @param results The list to collect results in
     */
    private void collectAllWords(TrieNode node, List<Long> results) {
        if (node.isEndOfWord() && node.getProductId() != null) {
            results.add(node.getProductId());
        }
        
        for (TrieNode child : node.getChildren().values()) {
            collectAllWords(child, results);
        }
    }
    
    /**
     * Checks if a word exists in the trie.
     * 
     * @param word The word to search for
     * @return true if the word exists, false otherwise
     */
    public boolean search(String word) {
        if (word == null || word.trim().isEmpty()) {
            return false;
        }
        
        TrieNode current = root;
        String normalizedWord = word.toLowerCase().trim();
        
        for (char ch : normalizedWord.toCharArray()) {
            if (!current.hasChild(ch)) {
                return false;
            }
            current = current.getChild(ch);
        }
        
        return current.isEndOfWord();
    }
    
    /**
     * Removes a word from the trie.
     * 
     * @param word The word to remove
     * @return true if the word was removed, false if it wasn't found
     */
    public boolean delete(String word) {
        if (word == null || word.trim().isEmpty()) {
            return false;
        }
        
        String normalizedWord = word.toLowerCase().trim();
        return deleteHelper(root, normalizedWord, 0);
    }
    
    /**
     * Helper method for recursive deletion.
     */
    private boolean deleteHelper(TrieNode current, String word, int index) {
        if (index == word.length()) {
            if (!current.isEndOfWord()) {
                return false; // Word doesn't exist
            }
            current.setEndOfWord(false);
            current.setProductId(null);
            
            // Return true if current has no children (can be deleted)
            return current.getChildren().isEmpty();
        }
        
        char ch = word.charAt(index);
        TrieNode node = current.getChild(ch);
        
        if (node == null) {
            return false; // Word doesn't exist
        }
        
        boolean shouldDeleteChild = deleteHelper(node, word, index + 1);
        
        if (shouldDeleteChild) {
            current.getChildren().remove(ch);
            
            // Return true if current has no children and is not end of another word
            return !current.isEndOfWord() && current.getChildren().isEmpty();
        }
        
        return false;
    }
    
    /**
     * Gets the total number of words in the trie.
     * 
     * @return The number of words stored in the trie
     */
    public int size() {
        return countWords(root);
    }
    
    /**
     * Recursively counts the number of words in the trie.
     */
    private int countWords(TrieNode node) {
        int count = 0;
        
        if (node.isEndOfWord()) {
            count = 1;
        }
        
        for (TrieNode child : node.getChildren().values()) {
            count += countWords(child);
        }
        
        return count;
    }
}
