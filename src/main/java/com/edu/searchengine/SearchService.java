package com.edu.searchengine;

import com.edu.model.Product;
import com.edu.repository.ProductRepository;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for handling product search operations.
 * Uses a Trie data structure for efficient prefix-based search.
 */
@Service
public class SearchService {
    private static final Logger logger = LoggerFactory.getLogger(SearchService.class);
    private static final int MAX_RESULTS = 10;
    
    private final Trie trie;
    private final ProductRepository productRepository;
    
    @Autowired
    public SearchService(ProductRepository productRepository) {
        this.trie = new Trie();
        this.productRepository = productRepository;
    }
    
    /**
     * Initializes the trie with all products from the database.
     */
    @PostConstruct
    public void initializeTrie() {
        logger.info("Initializing search engine trie...");
        List<Product> products = productRepository.findAll();
        
        for (Product product : products) {
            trie.insert(product.getName(), product.getId());
        }
        
        logger.info("Trie initialized with {} products", products.size());
    }
    
    /**
     * Searches for products that match the given query.
     * 
     * @param query The search query
     * @return List of products that match the query, ordered by popularity
     */
    public List<Product> search(String query) {
        if (query == null || query.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        // Get product IDs from trie that match the prefix
        List<Long> productIds = trie.searchByPrefix(query);
        
        if (productIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // Fetch products from database
        List<Product> matchedProducts = productRepository.findAllById(productIds);
        
        // Sort by popularity score
        matchedProducts.sort(Comparator.comparingDouble(Product::getPopularityScore).reversed());
        
        // Return top results
        return matchedProducts.stream()
                .limit(MAX_RESULTS)
                .collect(Collectors.toList());
    }
    
    /**
     * Adds a new product to the search engine.
     * 
     * @param product The product to add
     */
    public void addProduct(Product product) {
        trie.insert(product.getName(), product.getId());
        logger.info("Added product to search engine: {}", product.getName());
    }
    
    /**
     * Removes a product from the search engine.
     * 
     * @param product The product to remove
     */
    public void removeProduct(Product product) {
        trie.delete(product.getName());
        logger.info("Removed product from search engine: {}", product.getName());
    }
    
    /**
     * Updates a product in the search engine.
     * 
     * @param oldName The old product name
     * @param product The updated product
     */
    public void updateProduct(String oldName, Product product) {
        trie.delete(oldName);
        trie.insert(product.getName(), product.getId());
        logger.info("Updated product in search engine: {} -> {}", oldName, product.getName());
    }
    
    /**
     * Records a product click to update its popularity.
     * 
     * @param productId The ID of the clicked product
     */
    public void recordProductClick(Long productId) {
        productRepository.findById(productId).ifPresent(product -> {
            product.incrementClickCount();
            productRepository.save(product);
            logger.info("Recorded click for product: {}", product.getName());
        });
    }
    
    /**
     * Records a product view to update its popularity.
     * 
     * @param productId The ID of the viewed product
     */
    public void recordProductView(Long productId) {
        productRepository.findById(productId).ifPresent(product -> {
            product.incrementViewCount();
            productRepository.save(product);
            logger.info("Recorded view for product: {}", product.getName());
        });
    }
    
    /**
     * Gets the size of the search engine (number of products).
     * 
     * @return The number of products in the search engine
     */
    public int getSize() {
        return trie.size();
    }
}
