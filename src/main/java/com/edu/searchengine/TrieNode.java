package com.edu.searchengine;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents a node in the Trie data structure.
 * Each node contains a map of child nodes and a flag indicating if it's the end of a word.
 */
public class TrieNode {
    private Map<Character, TrieNode> children;
    private boolean isEndOfWord;
    private Long productId; // Reference to the product ID if this node marks the end of a product name
    
    public TrieNode() {
        this.children = new HashMap<>();
        this.isEndOfWord = false;
        this.productId = null;
    }
    
    // Getters and Setters
    public Map<Character, TrieNode> getChildren() {
        return children;
    }
    
    public boolean isEndOfWord() {
        return isEndOfWord;
    }
    
    public void setEndOfWord(boolean endOfWord) {
        isEndOfWord = endOfWord;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    /**
     * Checks if the node has a child with the given character.
     * 
     * @param ch The character to check
     * @return true if the child exists, false otherwise
     */
    public boolean hasChild(char ch) {
        return children.containsKey(ch);
    }
    
    /**
     * Gets the child node for the given character.
     * 
     * @param ch The character to get the child for
     * @return The child node, or null if it doesn't exist
     */
    public TrieNode getChild(char ch) {
        return children.get(ch);
    }
    
    /**
     * Adds a child node for the given character.
     * 
     * @param ch The character to add a child for
     * @return The newly created child node
     */
    public TrieNode addChild(char ch) {
        TrieNode node = new TrieNode();
        children.put(ch, node);
        return node;
    }
}
