package com.edu.dto;

/**
 * Data Transfer Object for search results.
 * Contains product information to be returned in API responses.
 */
public class SearchResultDTO {
    private Long id;
    private String name;
    private String description;
    private Double price;
    private String category;
    private Integer clickCount;
    private Integer viewCount;
    private Double popularityScore;
    
    // Default constructor
    public SearchResultDTO() {}
    
    // Constructor with all fields
    public SearchResultDTO(Long id, String name, String description, Double price, 
                          String category, Integer clickCount, Integer viewCount, 
                          Double popularityScore) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.clickCount = clickCount;
        this.viewCount = viewCount;
        this.popularityScore = popularityScore;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Integer getClickCount() {
        return clickCount;
    }
    
    public void setClickCount(Integer clickCount) {
        this.clickCount = clickCount;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Double getPopularityScore() {
        return popularityScore;
    }
    
    public void setPopularityScore(Double popularityScore) {
        this.popularityScore = popularityScore;
    }
}
