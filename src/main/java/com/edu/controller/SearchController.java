package com.edu.controller;

import com.edu.dto.SearchResultDTO;
import com.edu.model.Product;
import com.edu.searchengine.SearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * REST controller for handling search-related endpoints.
 */
@RestController
@RequestMapping("/api")
public class SearchController {
    private static final Logger logger = LoggerFactory.getLogger(SearchController.class);
    
    private final SearchService searchService;
    
    @Autowired
    public SearchController(SearchService searchService) {
        this.searchService = searchService;
    }
    
    /**
     * Endpoint for searching products by query.
     * 
     * @param query The search query
     * @return List of search results
     */
    @GetMapping("/search")
    public ResponseEntity<List<SearchResultDTO>> search(@RequestParam String query) {
        logger.info("Search request received: {}", query);
        
        List<Product> products = searchService.search(query);
        List<SearchResultDTO> results = convertToDTO(products);
        
        logger.info("Returning {} search results for query: {}", results.size(), query);
        return ResponseEntity.ok(results);
    }
    
    /**
     * Endpoint for recording a product click.
     * 
     * @param productId The ID of the clicked product
     * @return Success message
     */
    @PostMapping("/product/{productId}/click")
    public ResponseEntity<String> recordClick(@PathVariable Long productId) {
        logger.info("Recording click for product ID: {}", productId);
        searchService.recordProductClick(productId);
        return ResponseEntity.ok("Click recorded");
    }
    
    /**
     * Endpoint for recording a product view.
     * 
     * @param productId The ID of the viewed product
     * @return Success message
     */
    @PostMapping("/product/{productId}/view")
    public ResponseEntity<String> recordView(@PathVariable Long productId) {
        logger.info("Recording view for product ID: {}", productId);
        searchService.recordProductView(productId);
        return ResponseEntity.ok("View recorded");
    }
    
    /**
     * Converts Product entities to SearchResultDTO objects.
     * 
     * @param products List of products to convert
     * @return List of DTOs
     */
    private List<SearchResultDTO> convertToDTO(List<Product> products) {
        return products.stream()
                .map(product -> new SearchResultDTO(
                        product.getId(),
                        product.getName(),
                        product.getDescription(),
                        product.getPrice(),
                        product.getCategory(),
                        product.getClickCount(),
                        product.getViewCount(),
                        product.getPopularityScore()
                ))
                .collect(Collectors.toList());
    }
}
