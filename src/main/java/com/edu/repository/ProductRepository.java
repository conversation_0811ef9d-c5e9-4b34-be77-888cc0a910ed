package com.edu.repository;

import com.edu.model.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    // Find products by name containing the search term (case-insensitive)
    List<Product> findByNameContainingIgnoreCase(String name);
    
    // Find products by category
    List<Product> findByCategory(String category);
    
    // Find products ordered by popularity (click count + view count)
    @Query("SELECT p FROM Product p ORDER BY (p.clickCount * 2 + p.viewCount) DESC")
    List<Product> findAllOrderByPopularity();
    
    // Find products by name prefix (for autocomplete)
    @Query("SELECT p FROM Product p WHERE LOWER(p.name) LIKE LOWER(CONCAT(:prefix, '%')) ORDER BY (p.clickCount * 2 + p.viewCount) DESC")
    List<Product> findByNameStartingWithOrderByPopularity(@Param("prefix") String prefix);
    
    // Find top N products by popularity
    @Query("SELECT p FROM Product p ORDER BY (p.clickCount * 2 + p.viewCount) DESC")
    List<Product> findTopProductsByPopularity();
}
