package com.edu.model;

import jakarta.persistence.*;

@Entity
@Table(name = "products")
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column
    private String description;
    
    @Column
    private Double price;
    
    @Column(name = "click_count")
    private Integer clickCount = 0;
    
    @Column(name = "view_count")
    private Integer viewCount = 0;
    
    @Column
    private String category;
    
    // Default constructor
    public Product() {}
    
    // Constructor with essential fields
    public Product(String name, String description, Double price, String category) {
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.clickCount = 0;
        this.viewCount = 0;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public Integer getClickCount() {
        return clickCount;
    }
    
    public void setClickCount(Integer clickCount) {
        this.clickCount = clickCount;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    // Utility methods
    public void incrementClickCount() {
        this.clickCount++;
    }
    
    public void incrementViewCount() {
        this.viewCount++;
    }
    
    // Calculate popularity score (can be customized)
    public double getPopularityScore() {
        return (clickCount * 2.0) + (viewCount * 1.0);
    }
    
    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", price=" + price +
                ", clickCount=" + clickCount +
                ", viewCount=" + viewCount +
                ", category='" + category + '\'' +
                '}';
    }
}
